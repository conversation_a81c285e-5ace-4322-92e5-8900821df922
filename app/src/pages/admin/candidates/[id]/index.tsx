import React, { useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  closeDialog,
  getCandidateDetail,
  openDialog,
  setLoader,
} from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import {
  CandidateEducation,
  CandidateExperience,
  CandidateProfessionalSummary,
  CandidateSkills,
} from "@src/components/WildCard";
import { Button, ListGroup } from "react-bootstrap";
import Link from "next/link";
import { candidateApi } from "@src/apis/wildcardApis";
import {
  useEmployeePagePermissions,
  useEmployeeSelectedPagePermissions,
} from "@src/helper/pagePermissions";
import {
  CandidateInterface,
  EmailSubjectContentInterface,
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { encrypt, encryptString } from "@src/helper/encryption";
import {
  Mail as MailIcon,
  Call as CallIcon,
  Edit as EditIcon,
  AssignmentTurnedIn as AssignmentTurnedInIcon,
  Download as DownloadIcon,
  Science as ScienceIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
} from "@mui/icons-material";
import AddCommentIcon from "@mui/icons-material/AddComment";
import { Card, Tab, Tabs } from "react-bootstrap";

import DialogComponents from "@src/components/DialogComponents";
import { decrypt } from "@src/helper/encryption";
import { CurrentTime } from "@src/components/Common";
import { downloadFile } from "@src/helper/downloadFile";
import Image from "next/image";
import { buildTemplate } from "@src/static/html";
import { CandidateRatingReview } from "@src/components/WildCard/Candidate/CandidateDetail/CandidateRatingReview";
import { getFileNameFromPresignedUrl } from "@src/helper/common";
import { CandidateDocuments } from "@src/components/WildCard/Candidate/CandidateDetail/CandidateDocuments";
import { CandidateProfileComments } from "@src/components/WildCard/Candidate/CandidateDetail/CandidateProfileComment";
import { SimilarCandidateCard } from "@src/components/WildCard/Candidate/CandidateDetail/CandidateSimilarCandidateCard";

type DepartmentPageProps = {
  encryptedId: string;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};

export default function CandidatePage({
  pagePermissions,
  encryptedId,
}: DepartmentPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const id = decrypt(encryptedId);
  const [commentReload, setCommentReload] = useState<number>(1);
  const candidate = useSelector(
    (state: RootState) => state.candidate.candidate,
  );
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const currentEmployeeRole: string = currentEmployee?.employee_role ?? "";
  const currentEmployeeId: number = currentEmployee?.id ?? 0;
  let interviewerOrAdminCandidate =
    (currentEmployeeRole == "Interviewer" &&
      currentEmployeeId == candidate?.created_by_id) ||
    currentEmployeeRole != "Interviewer";

  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");

  const fetchAndSetCandidate = async () => {
    await dispatch(setLoader(true));
    await dispatch(
      getCandidateDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  const requestDocumentEmail = async (state?: KeyPairInterface) => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateApi.sendDocumentRequestEmail(
      id,
      state,
    );
    await dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      dispatch(closeDialog());
    }
  };

  const requestDocumentEmailConfirmation = () => {
    dispatch(
      openDialog({
        config: DialogComponents.REQUEST_DOCUMENT_FORM_MODAL,
        options: {
          title: "Request Document Confirmation",
          candidateId: id,
          onConfirm: (state: KeyPairInterface) => requestDocumentEmail(state),
        },
      }),
    );
  };

  const updateCandidateStatus = async (id: number, status: number) => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateApi.updateCandidateStatus(id, {
      status: status,
    });
    flashMessage(message, success ? "success" : "error");
    fetchAndSetCandidate();
    await dispatch(setLoader(false));
  };

  const sendEmail = async (data: EmailSubjectContentInterface) => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateApi.sendEmail(id, data);
    await dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      dispatch(closeDialog());
    }
  };

  const openSendEmailModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIATE_JOB_EMAIL_MODAL,
        options: {
          title: "Send Email",
          candidate: candidate,
          onSendEmail: sendEmail,
          buildTemplate: (content: string) =>
            // buildCandidateEmailTemplate(candidate?.name ?? "", content),
            buildTemplate(content),
        },
      }),
    );
  };

  const onSubmitComment = () => {
    setCommentReload((prev) => prev + 1);
  };

  const openCommentModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_PROFILE_COMMENT_MODAL,
        options: {
          title: "Candidate Profile Comment",
          candidate: candidate,
          onSubmitComment: onSubmitComment,
        },
      }),
    );
  };

  useEffect(() => {
    fetchAndSetCandidate();
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [id]);

  if (!candidate) {
    return null;
  }

  const openInterviewExistModal = (candidate: CandidateInterface) => {
    const interview = candidate.interviews && candidate.interviews[0];
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Interview already In-Progress",
          message: (
            <div className="mt-2 mb-2">
              The interview for this candidate has already been scheduled under
              the job
              <strong className="ml-1">
                {interview?.opportunity_title ?? ""}
              </strong>
              .
            </div>
          ),
        },
      }),
    );
  };

  const hasEditPermission = currentPagePermissions.includes("edit");
  const hasWriteInterviewPermission = interviewsPermissions.includes("write");
  let encryptedJobId: string | undefined =
    candidate.opportunity_id && candidate.opportunity_id != 0
      ? encryptString(candidate.opportunity_id.toString())
      : undefined;
  const jobParams = new URLSearchParams();
  if (encryptedJobId) {
    jobParams.append("job_id", encryptedJobId);
  }

  return (
    <>
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">Candidates</h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            Candidate Profile
          </h4>
        </div>
        <CurrentTime />
      </div>

      <div className="row">
        <div className="col-lg-12 col-xl-8">
          <Card>
            <Card.Body>
              <div className="profile-card-wrap d-flex gap-3">
                <div className="profile-head w-100">
                  <div className="d-flex justify-content-between align-items-start flex-wrap">
                    <h2 className="m-0">
                      {" "}
                      {candidate.name || candidate.email}
                    </h2>
                    <ul className="profile-more-option list-inline mb-0 d-flex align-items-center">
                      <li className="list-inline-item">
                        {hasEditPermission && interviewerOrAdminCandidate && (
                          <Link
                            href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}/edit`}
                            className="no-decoration">
                            <EditIcon className="material-icons cursor" />
                          </Link>
                        )}
                      </li>
                    </ul>
                  </div>

                  <div className="row">
                    <div className="col-12">
                      <p className="candidate-designation">
                        {candidate.designation ?? ""}
                      </p>

                      <div className="candidate-actions d-flex justify-content-between align-items-start flex-wrap">
                        <div className="casndi-contact">
                          <span>
                            <ScienceIcon className="material-icons" />
                          </span>

                          <div className="email-data w-100">
                            <p className="mb-0 heading-clr fw-medium">
                              {candidate.is_fresher ? (
                                <>Candidate is Fresher</>
                              ) : (
                                <>{candidate.total_experience ?? 0} + years</>
                              )}
                            </p>
                            <p className="mb-0 text-light-clr">Experience</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>

          <Card>
            <Card.Body>
              <Tabs
                defaultActiveKey="candidate-detail"
                id="uncontrolled-tab-example"
                className="candidate-profile-tabs mb-3">
                <Tab eventKey="candidate-detail" title="Candidate Details">
                  <div className="tab-content">
                    <div className="tab-pane fade show active candi-details-tab">
                      <CandidateProfessionalSummary candidate={candidate} />

                      <CandidateExperience
                        experiences={candidate.experiences}
                      />

                      <CandidateEducation
                        educations={candidate.educations_informations}
                      />
                      <CandidateSkills skills={candidate.skills} />
                    </div>
                  </div>
                </Tab>
                <Tab eventKey="candidate-feedback" title="Rating & Review">
                  <div className="tab-content">
                    <div className="tab-pane fade show active candi-details-tab">
                      <CandidateRatingReview
                        id={candidate.id}
                        showJob
                        fetchFunction={candidateApi.getFeedbacks as any}
                      />
                    </div>
                  </div>
                </Tab>
                <Tab eventKey="candidate-documents" title="Documents">
                  <div className="tab-content">
                    <div className="tab-pane fade show active candi-details-tab">
                      <CandidateDocuments
                        id={candidate.id}
                        fetchFunction={candidateApi.getDocuments as any}
                      />
                    </div>
                  </div>
                </Tab>
                <Tab eventKey="candidate-comments" title="Comments">
                  <div className="tab-content">
                    <div className="tab-pane fade show active candi-details-tab">
                      <CandidateProfileComments
                        id={candidate.id}
                        fetchFunction={candidateApi.getProfileComments as any}
                        reload={commentReload}
                      />
                    </div>
                  </div>
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </div>

        <div className="col-lg-12 col-xl-4">
          <Card className="p-3">
            <Card.Body className="p-0">
              <Card.Title className="border-bottom pb-3 fw-semibold heading-clr">
                Contact Details
              </Card.Title>
              <div className="d-flex align-items-center justify-content-between border-bottom pb-3 mb-3 flex-wrap gap-3">
                <div className="casndi-contact">
                  <span>
                    <CallIcon className="material-icons" />
                  </span>
                  <div className="contact-data">
                    <p className="mb-0 heading-clr fw-medium">
                      {candidate?.contact}
                    </p>
                    <p className="mb-0 text-light-clr">Phone Number</p>
                  </div>
                </div>
                <div className="casndi-contact">
                  <span>
                    <MailIcon className="material-icons" />
                  </span>

                  <div className="email-data w-100">
                    <p className="mb-0 heading-clr fw-medium">
                      {candidate?.email}
                    </p>
                    <p className="mb-0 text-light-clr">Email Address</p>
                  </div>
                </div>

                <div className="candi-other-links">
                  <div className="justify-content-center d-flex gap-2 align-items-center">
                    {candidate.website ||
                    candidate.github ||
                    candidate.linkedin ? (
                      <label className="text-light-clr">Social-Links:</label>
                    ) : null}
                    <ListGroup horizontal className="list-inline mb-0">
                      {candidate.website ? (
                        <ListGroup.Item className="list-inline-item">
                          <Link href={candidate.website} target="_blank">
                            <Image
                              src="/images/website.svg"
                              width={30}
                              height={20}
                              alt="icon"
                            />
                          </Link>
                        </ListGroup.Item>
                      ) : null}
                      {candidate.github ? (
                        <ListGroup.Item className="list-inline-item">
                          <Link href={candidate.github} target="_blank">
                            <Image
                              src="/images/behance.svg"
                              width={30}
                              height={20}
                              alt="icon"
                            />
                          </Link>
                        </ListGroup.Item>
                      ) : null}
                      {candidate.linkedin ? (
                        <ListGroup.Item className="list-inline-item">
                          <Link href={candidate.linkedin} target="_blank">
                            <Image
                              src="/images/dribbble.svg"
                              width={30}
                              height={20}
                              alt="icon"
                            />
                          </Link>
                        </ListGroup.Item>
                      ) : null}
                    </ListGroup>
                  </div>
                </div>
              </div>

              {(hasEditPermission || hasWriteInterviewPermission) &&
                candidate.blacklist == 0 &&
                interviewerOrAdminCandidate && (
                  <>
                    <div className="d-flex align-items-center justify-content-between border-bottom pb-3 mb-3 flex-wrap">
                      <div className="w-100 mb-3">
                        <p className="mb-0 text-light-clr">
                          Schedule Interview
                        </p>
                        <p className="m-0">
                          Click the button below to schedule an interview for
                          the candidate.
                        </p>
                      </div>

                      {candidate.has_interview ? (
                        <>
                          <Button
                            className="mx-auto d-inline-flex align-items-center justify-content-center gap-2"
                            variant="theme"
                            onClick={() => openInterviewExistModal(candidate)}>
                            <ScheduleIcon className="material-icons" />
                            Schedule Interview
                          </Button>
                        </>
                      ) : (
                        <Link
                          className="mx-auto"
                          href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}/interview?${jobParams.toString()}`}>
                          <Button
                            className="d-inline-flex align-items-center justify-content-center gap-2"
                            variant="theme"
                            disabled={candidate.has_interview}>
                            <ScheduleIcon className="material-icons" />
                            Schedule Interview
                          </Button>
                        </Link>
                      )}
                    </div>
                  </>
                )}

              {hasEditPermission &&
                interviewerOrAdminCandidate &&
                candidate.blacklist == 0 && (
                  <>
                    <div className="d-flex align-items-center justify-content-between border-bottom pb-3 mb-3 flex-wrap">
                      <div className="w-100 mb-3">
                        <p className="mb-0 text-light-clr">Send Email</p>
                        <p className="m-0">
                          Click the button below to send a email to candidate.
                        </p>
                      </div>

                      <Button
                        className="mx-auto d-inline-flex align-items-center justify-content-center gap-2"
                        onClick={openSendEmailModal}
                        variant="theme">
                        <EmailIcon className="material-icons" />
                        Send Email
                      </Button>
                    </div>
                  </>
                )}

              {/* Resume Download */}

              <div className="resume-wrap d-flex align-items-center justify-content-between border-bottom pb-3 mb-3 flex-wrap">
                <div className="w-100 mb-3">
                  <p className="mb-0 text-light-clr">Resume</p>
                  <p className="mb-0 heading-clr">
                    {getFileNameFromPresignedUrl(candidate.resume_url)}
                  </p>
                </div>

                <Button
                  onClick={() => downloadFile(candidate.resume_url)}
                  variant=""
                  disabled={!candidate.resume_url || candidate.resume_url == ""}
                  className="no-underline text-white download-btn mx-auto d-inline-flex align-items-center justify-content-center gap-2">
                  <DownloadIcon className="material-icons" />
                  Download
                </Button>
              </div>

              {/*Adding Comments*/}

              <div className="d-flex align-items-center justify-content-between border-bottom pb-3 mb-3 flex-wrap">
                <div className="w-100 mb-3">
                  <p className="mb-0 text-light-clr">Add Comments</p>
                  <p className="m-0">
                    Click the button below to add the comments on the candidate
                    profile Which can be view by others.
                  </p>
                </div>

                <Button
                  className="mx-auto d-inline-flex align-items-center justify-content-center gap-2"
                  onClick={openCommentModal}
                  variant="theme">
                  <AddCommentIcon className="material-icons" />
                  Add Comment
                </Button>
              </div>

              {hasEditPermission &&
                interviewerOrAdminCandidate &&
                candidate.blacklist == 0 && (
                  <>
                    <div className="w-100 mb-3">
                      <p className="mb-1 text-light-clr">Request Documents</p>
                      <p className="mb-1 text-justify">
                        Click the button below to request the candidate&apos;s
                        documents. We will send an email to the candidate to
                        collect the required documents.
                      </p>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="theme"
                        onClick={() => requestDocumentEmailConfirmation()}
                        className="d-inline-flex justify-content-center align-items-center gap-2">
                        <AssignmentTurnedInIcon className="material-icons" />
                        Request Document by email
                      </Button>
                    </div>
                  </>
                )}
            </Card.Body>
          </Card>
          <SimilarCandidateCard
            designation={candidate.designation}
            candidateId={candidate.id}
          />
        </div>
      </div>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain, allowedRoles, pagePermissions } =
    await extractSubdomainAndDomain(req, { wildcard: true }, "candidates");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const encryptedId = String(query.id) as string;
  return {
    props: {
      subdomain: subdomain,
      encryptedId: encryptedId,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
    },
  };
};

CandidatePage.layout = PrivateLayout;
