import React, { useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { useRouter } from "next/router";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { PagePermissionInterface } from "@src/redux/interfaces";
import AllWarningsShow from "@src/components/WildCard/Interview/AllWarningsShow";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";

type WarningsProps = {
  id: number;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};

export default function WarningsPage({ id }: WarningsProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const round: any = router.query.round || "";

  const [warnings, setWarnings] = useState<any>([]);

  useEffect(() => {
    getWarnings();
  }, []);

  const getWarnings = async () => {
    dispatch(setLoader(true));
    const { success, ...response } =
      await interviewsApi.getInterviewWarning(round);
    console.log(response, "response");

    setWarnings(response);
    dispatch(setLoader(false));
  };

  return (
    <>
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">Interview Management</h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            Warnings
          </h4>
        </div>
      </div>

      <div className="row">
        <AllWarningsShow />
      </div>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "interviews",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "edit"],
      pageDetail: pageDetail,
    },
  };
};

WarningsPage.layout = PrivateLayout;
