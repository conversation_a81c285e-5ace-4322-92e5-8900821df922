import React from "react";
import { But<PERSON> } from "antd";
import flashMessage from "@src/components/FlashMessage";
import Image from "next/image";
import Script from "next/script";
import { setLoader } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import { DROPBOX_APP_KEY } from "@src/constants";

// Props for the DropboxResume component
type DropboxResumeProps = {
  disabled: boolean;
  onSetResumes: (files: any[]) => void;
};

// Component for the modal to add a new candidate
export const DropboxResume = ({
  onSetResumes,
  disabled,
}: DropboxResumeProps) => {
  // ======================== Dropbox functions =============================
  const dispatch = useAppDispatch();

  const openDropbox = () => {
    if (!window.Dropbox) {
      flashMessage("There is issue in dropbox. Please try again.", "error");
      return;
    }

    window.Dropbox.choose({
      success: async (files: any) => {
        dispatch(setLoader(true));

        const allFilesPromises = files.map(async (file: any) => {
          const response = await fetch(file.link);
          const blob = await response.blob();
          const fileObj = new File([blob], file.name, { type: blob.type });
          return {
            uid: file.id,
            name: file.name,
            status: "done",
            originFileObj: fileObj,
          };
        });

        const allFiles = await Promise.all(allFilesPromises);
        onSetResumes(allFiles);
        dispatch(setLoader(false));
      },
      cancel: () => {
        // "User canceled Dropbox chooser."
      },
      linkType: "direct",
      multiselect: true,
      maxFiles: 5,
      extensions: [".pdf", ".docx"],
    });
  };

  // ======================== Dropbox functions =============================

  return (
    <>
      {/* Dropbox Script */}
      <Script
        src="https://www.dropbox.com/static/api/2/dropins.js"
        id="dropboxjs"
        strategy="afterInteractive"
        data-app-key={DROPBOX_APP_KEY}
        onLoad={() => {
          if (window.Dropbox) {
            console.log("Dropbox SDK loaded successfully.");
          } else {
            console.error("Dropbox failed to load.");
          }
        }}
      />

      <Button
        className="cloud-select-picker-button"
        onClick={openDropbox}
        disabled={disabled}>
        <div>
          <Image
            src={"/images/icons/dropbox.svg"}
            height={78}
            width={88}
            alt=""
          />
        </div>
        Upload From Dropbox
      </Button>
    </>
  );
};
