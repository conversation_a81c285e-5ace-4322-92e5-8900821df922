from fastapi import (
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
    Body,
    UploadFile,
    File,
    Form,
)
from app.schema import PaginationResponse, SuccessResponse
from app.models import Employee, EmployeeSignature, Business
from app.exceptions import RecordNotFoundException
from app.helper import WildcardAuthHelper
from app.uploader.image_uploader import ImageUploader
from typing import Optional
from peewee import fn
from datetime import datetime
import base64
import logging

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/employee-signatures",
    tags=["Wildcard Employee Signature API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


def get_employee_signature(
    employee_id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Dependency to get an employee signature by ID.

    Args:
        id (int): The ID of the employee signature.

    Returns:
        EmployeeSignature: The employee signature object.

    Raises:
        RecordNotFoundException: If the employee signature is not found.
    """

    employee = Employee.get_or_none(id=employee_id, business_id=business.id)
    if not employee or not employee.employee_signatures.exists():
        raise RecordNotFoundException(message="Employee signature not found")

    return employee.employee_signatures.first()


@router.get(
    "",
    summary="Get Employee Signatures",
    description="Retrieve a paginated list of employee signatures.",
    response_model=PaginationResponse,
    dependencies=[Depends(WildcardAuthHelper.employee_is_super_admin)],
)
def get_employee_signatures(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search query based on employee name or email"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of employee signatures.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of signatures per page. Defaults to 10.
        search (Optional[str]): Search term to filter by employee name or email.
        employee (Employee): The current employee, provided by dependency injection.

    Returns:
        PaginationResponse: Paginated list of employee signatures.
    """
    try:
        # Simple query first - get all signatures for this business
        all_signatures = (
            EmployeeSignature.select()
            .join(Employee, on=(EmployeeSignature.employee_id == Employee.id))
            .where(Employee.business_id == business.id)
        )

        if search:
            search = search.strip().lower()
            search_filter = (
                (fn.lower(Employee.first_name).contains(search))
                | (fn.lower(Employee.last_name).contains(search))
                | (fn.lower(Employee.email).contains(search))
                | (
                    fn.lower(
                        fn.CONCAT(Employee.first_name, " ", Employee.last_name)
                    ).contains(search)
                )
            )
            all_signatures = all_signatures.where(search_filter)

        total_count = all_signatures.count()

        offset = (page - 1) * limit
        signatures = all_signatures.offset(offset).limit(limit)

        return PaginationResponse(
            message="Employee signatures retrieved successfully",
            data={
                "rows": [signature.info() for signature in signatures],
                "count": total_count,
                "page": page,
                "limit": limit,
            },
        )
    except Exception as e:
        logging.error(f"Exception in get employee signatures: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving employee signatures",
        )


@router.get(
    "/{employee_id}",
    summary="Get Employee Signature Info",
    description="Get employee signature information by ID",
    response_model=SuccessResponse,
    dependencies=[Depends(WildcardAuthHelper.employee_is_super_admin)],
)
def get_employee_signature_detail(
    signature: EmployeeSignature = Depends(get_employee_signature),
):
    """
    Retrieve detailed information about a specific employee signature.

    Args:
        signature (EmployeeSignature): The signature object retrieved from the database.
        employee (Employee): The current employee, provided by dependency injection.

    Returns:
        SuccessResponse: Detailed information about the employee signature.
    """
    try:
        # Verify that the signature belongs to an employee in the same business
        return SuccessResponse(
            message="Employee signature retrieved successfully", data=signature.info()
        )
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Exception in get employee signature detail: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving the employee signature",
        )


@router.post(
    "",
    summary="Create Employee Signature",
    description="Create a new employee signature.",
    response_model=SuccessResponse,
    dependencies=[Depends(WildcardAuthHelper.employee_is_super_admin)],
)
async def create_employee_signature(
    image: UploadFile = File(..., description="Signature Image"),
    employee_id: str = Form(..., description="Employee Id"),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Endpoint to create a new employee signature via form data.

    Args:
        current_employee (Employee): The current employee.

    Returns:
        SuccessResponse: A response indicating success or failure.
    """
    try:
        target_employee = Employee.get_or_none(
            Employee.id == int(employee_id),
            Employee.business_id == current_employee.business_id,
        )
        if not target_employee:
            raise RecordNotFoundException(message="Employee does not exist")
        else:
            target_employee = current_employee

        uploader = ImageUploader(
            image,
            image.filename,
            f"employees/{employee_id}/signatures/{int(datetime.now().timestamp())}",
        )
        uploaded_path = await uploader.upload()
        signature_path = uploaded_path["file_path"]

        existing_signature = EmployeeSignature.get_or_none(
            EmployeeSignature.employee_id == employee_id
        )

        file_bytes = await image.read()
        # Encode to base64
        base64_sign = base64.b64encode(file_bytes).decode("utf-8")

        if existing_signature:
            # Update existing signature instead of creating new one
            existing_signature.signature = signature_path
            existing_signature.base64_sign = base64_sign
            existing_signature.save()

            return SuccessResponse(
                message="Employee signature updated successfully.",
                data=existing_signature.info(),
            )

        # Create new employee signature
        EmployeeSignature.create(
            employee_id=employee_id,
            signature=signature_path,
            base64_sign=base64_sign,
        )

        return SuccessResponse(
            message="Employee signature created successfully.",
        )
    except RecordNotFoundException as cus_exc:
        logging.info(
            f"Exception in create employee RecordNotFoundException: {str(cus_exc)}"
        )
        raise cus_exc
    except HTTPException as http_exc:
        logging.info(f"Exception in create employee HTTPException: {str(http_exc)}")
        raise http_exc
    except Exception as e:
        logging.error(f"Exception in create employee signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the employee signature",
        )


@router.patch(
    "/{employee_id}/status",
    summary="Update Employee Signature",
    description="Update an existing employee signature",
    response_model=SuccessResponse,
    dependencies=[Depends(WildcardAuthHelper.employee_is_super_admin)],
)
async def update_employee_signature(
    signature: EmployeeSignature = Depends(get_employee_signature),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update an existing employee signature.

    Args:
        signature (EmployeeSignature): The signature object to update.
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing updated signature information.

    Returns:
        SuccessResponse: A response indicating success or failure.
    """

    try:
        # Verify that the signature belongs to an employee in the same business
        signature.status = body.get("status")
        # Save the updated signature
        signature.save()

        return SuccessResponse(
            message="Employee signature status updated successfully.",
            data=signature.info(),
        )
    except Exception as e:
        logging.error(f"Exception in update employee signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the employee signature",
        )


@router.get(
    "/option/all-active-signatures",
    summary="Get All Active Employee Signatures",
    description="Retrieve all active employee signatures for the current business.",
    response_model=SuccessResponse,
)
def get_all_active_signatures(
    search: Optional[str] = Query(
        None, description="Search term to filter opportunities"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve all active employee signatures for the current business.

    Args:
        search (Optional[str]): Search term to filter Employee first name.
        employee (Employee): The current employee, provided by dependency injection.

    Returns:
        SuccessResponse: List of all active employee signatures.
    """
    try:
        base_query = (
            Employee.select()
            .join(EmployeeSignature, on=(EmployeeSignature.employee_id == Employee.id))
            .where(
                (Employee.business_id == business.id) & (EmployeeSignature.status == 1)
            )
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.LOWER(Employee.first_name).contains(search)
            )

        rows = [
            {
                "label": f"{record.full_name()} - {record.email}",
                "value": record.id,
            }
            for record in base_query
        ]

        return SuccessResponse(
            message="Active employee signatures retrieved successfully",
            data=rows,
        )
    except Exception as e:
        logging.error(f"Exception in get all active signatures: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving active employee signatures",
        )
