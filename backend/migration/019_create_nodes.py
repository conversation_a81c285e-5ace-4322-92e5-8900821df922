"""
Peewee migrations -- 019_create_nodes.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
    CREATE TABLE nodes (
        id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        unique_key VARCHAR(255) NOT NULL,
        path VARCHAR(255),
        icon VARCHAR(255),
        order_num INT,
        parent_id BIGINT,
        singular_name VARCHAR(255) NOT NULL,
        description TEXT,
        type VARCHAR(255) NOT NULL DEFAULT "AdminNode",
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES nodes(id),
        UNIQUE INDEX idx_nodes_unique_key (unique_key)
    );
    """

    # create table candidate_skills
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_nodes_type ON nodes (type);")
    migrator.sql("CREATE INDEX idx_nodes_order_num ON nodes (order_num);")

    # insert queries
    sql_query = """
        INSERT INTO nodes (name, singular_name, unique_key, path, type, order_num, icon, description) VALUES
            ('Dashboard', 'Dashboard', 'admin_dashboard', '/admin/dashboard', 'AdminNode', 1, 'dashboard', 'Welcome! to Recruitease Pro'),
            ('Business Management', 'Business', 'admin_business', '/admin/business', 'AdminNode', 2, 'employee', 'Centralized platform designed to streamline operations, enhance collaboration, and improve decision-making by integrating various business processes and tools.'),
            ('Dashboard', 'Dashboard', 'employee_dashboard', '/admin/dashboard', 'EmployeeNode', 1, 'dashboard', 'Welcome! to Recruitease Pro'),
            ('Location Management', 'Location', 'employee_locations', '/admin/locations', 'EmployeeNode', 2, 'location', 'Location management is the process of tracking and managing the physical location of company.'),
            ('Staff Management', 'Staff', 'employee_employees', '/admin/employees', 'EmployeeNode', 3, 'employee', 'Effectively oversee and manage your team members. Designate specific roles and responsibilities by assigning users as administrators, human resources personnel, or interviewers.'),
            ('Department Management', 'Department', 'employee_departments', '/admin/departments', 'EmployeeNode', 4, 'department', 'To effectively organize and classify your job postings, its important to group them into distinct departments. This approach ensures clarity and helps candidates easily find relevant job opportunities.'),
            ('Jobs Management', 'Job', 'employee_opportunities', '/admin/opportunities', 'EmployeeNode', 5, 'opportunity', 'Provide comprehensive information about your job posting. Include essential details such as job description, qualifications, responsibilities, and any other relevant information.'),
            ('Candidate Management', 'Candidate',  'employee_candidates', '/admin/candidates', 'EmployeeNode', 6, 'candidate', 'Identify and select the most suitable candidates using advanced AI technology. Our system analyzes candidate data and provides scores to help you make informed hiring decisions.'),
            ('Schedule Interview', 'Schedule Interview', 'employee_interviews', '/admin/interviews', 'EmployeeNode', 7, 'schedule', 'Present a comprehensive schedule of upcoming interviews, detailing candidate names, interview dates, times etc'),
            ('Role Permissions', 'Role Permission', 'employee_settings', '/admin/node_settings', 'EmployeeNode', 8, 'permission', 'Customize data accessibility for different user groups. Grant permissions to read, write, edit, or delete specific data fields to maintain data integrity and security.')
            ('Hiring Managements', 'Hiring Management', 'analytics_management', '/admin/analytics_management', 'EmployeeNode', 8, 'analytics_management', 'View and analyze hiring-related statistics. Hiring Management provides users with insights into candidate pipelines, interview performance, and recruitment progress across departments.')
            ('Signature Managements', 'Signature Management', 'signature_management', '/admin/signature_managements', 'EmployeeNode', 9, 'signature_management', 'Manage and oversee digital signatures within the system. Signature Management enables users to track, authenticate, and securely sign documents, ensuring compliance and efficiency across various workflows and departments.')
            """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_nodes_type ON nodes;")
    migrator.sql("DROP INDEX idx_nodes_order_num ON nodes;")

    # drop table
    migrator.sql("DROP TABLE `nodes`")
